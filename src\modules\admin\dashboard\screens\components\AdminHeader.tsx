'use client'

import Logo from '@/assets/svgs/Logo'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useNavigation } from '@/hooks/useNavigation'
import { useFetchUserProfile, useUser, useUserLoading } from '@/modules/user/stores/user.store'
import { clearAuthTokens } from '@/services/auth'
import { appPaths } from '@/utils/app-routes'
import { LogOut, Settings, User } from 'lucide-react'
import React from 'react'

export const AdminHeader: React.FC = () => {
  const { navigate } = useNavigation()
  const user = useUser()
  const fetchProfile = useFetchUserProfile()
  const isLoading = useUserLoading()

  React.useEffect(() => {
    if (!user) {
      fetchProfile()
    }
  }, [user, fetchProfile])

  const handleLogout = () => {
    clearAuthTokens()
    navigate(appPaths.auth.login())
  }

  return (
    <header className="w-full flex items-center justify-between py-4 px-2 md:px-6 bg-white border-b border-gray-100 shadow-sm mb-6">
      <div className="flex items-center gap-3">
        <Logo width={40} height={40} />
        <span className="font-bold text-xl tracking-tight text-primary">PICKSLOT</span>
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button
            type="button"
            className="flex items-center gap-2 outline-none border-none bg-transparent cursor-pointer"
          >
            <Avatar className="size-10">
              <AvatarImage src={user?.avatar || undefined} alt={user?.name || ''} />
              <AvatarFallback>{user?.name?.[0] || '?'}</AvatarFallback>
            </Avatar>
            <span className="text-xs font-bold text-gray-500">{isLoading ? '...' : (user?.name || '...')}</span>
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel className="flex flex-col gap-0.5">
            <span className="font-semibold">{isLoading ? '...' : (user?.name || '...')}</span>
            <span className="text-xs text-gray-500">{isLoading ? '' : (user?.email || '')}</span>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <User className="mr-2 h-4 w-4" />
            Profile
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleLogout} className="text-red-600">
            <LogOut className="mr-2 h-4 w-4" />
            Logout
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </header>
  )
}

export default AdminHeader
