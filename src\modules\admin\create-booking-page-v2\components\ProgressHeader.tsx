'use client'

import type { CreateBookingPagePayload } from '../../apis/booking-page.api'

import { Button } from '@/components/ui/button'
import { appPaths } from '@/utils/app-routes'
import { Check, ChevronLeft, ChevronRight, FileText, Palette, Settings, X } from 'lucide-react'
import { useRouter } from 'next/navigation'
import React from 'react'
import { useAsyncFn } from 'react-use'
import { toast } from 'sonner'
import { bookingPageAPIs } from '../../apis/booking-page.api'
import { pageInfoStepRef } from '../constants/ref'
import { pageInfoSchema, templateSelectionSchema } from '../schemas/form-schemas'
import { useCreateBookingPageV2Store } from '../stores/create-booking-page-v2.store'

const STEPS = [
  { id: 1, title: 'Thông tin', icon: FileText },
  { id: 2, title: 'Chọn mẫu', icon: Palette },
  { id: 3, title: '<PERSON><PERSON><PERSON> hình', icon: Settings },
]

const TOTAL_STEPS = 3

interface ProgressHeaderProps {
  isEditMode?: boolean
  bookingPageId?: string
}

export const ProgressHeader: React.FC<ProgressHeaderProps> = ({
  isEditMode = false,
  bookingPageId,
}) => {
  const { currentStep, setCurrentStep } = useCreateBookingPageV2Store()
  const router = useRouter()

  const {
    nextStep,
    prevStep,
    validateCurrentStep,
    pageInfo,
    selectedTemplateId,
    bookingConfig,
  } = useCreateBookingPageV2Store()

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return pageInfoSchema.safeParse(pageInfo)?.success
      case 2:
        return templateSelectionSchema.safeParse({ selectedTemplateId }).success
      case 3:
        return (
          bookingConfig.openTime !== ''
          && bookingConfig.closeTime !== ''
          && bookingConfig.fields.length > 0
        )
      default:
        return false
    }
  }

  const handleNext = () => {
    if (validateCurrentStep()) {
      nextStep()
    }
  }

  const [state, fetchData] = useAsyncFn(async (payload: CreateBookingPagePayload) => {
    let response
    if (isEditMode && bookingPageId) {
      // Update existing booking page
      response = await bookingPageAPIs.updateBookingPage(bookingPageId, payload)
    } else {
      // Create new booking page
      response = await bookingPageAPIs.createBookingPage(payload)
    }
    return response
  }, [isEditMode, bookingPageId])

  const handleFinish = async () => {
    if (!validateCurrentStep()) {
      return
    }
    try {
      const payload: CreateBookingPagePayload = {
        name: pageInfo.name,
        description: pageInfo.description,
        slug: pageInfo.slug,
        templateCode: selectedTemplateId,
        blocks: [{
          type: 'config',
          data: {
            ...bookingConfig,
          },
        }],
        theme: {
          primaryColor: '#FF5722',
          fontFamily: 'Roboto',
          layout: 'default' as any,
        },
      }

      const response = await fetchData(payload)

      if (response?.status?.success) {
        const successMessage = isEditMode ? 'Cập nhật trang đặt chỗ thành công!' : 'Tạo trang đặt chỗ thành công!'
        toast.success(successMessage)
        router.push(appPaths.admin.manageBookingPages())
      } else {
        // Xử lý lỗi slug đã tồn tại
        if (response?.status?.code === 'booking-page/slug-exists') {
          toast.error('Slug đã tồn tại, vui lòng chọn slug khác!')
          setCurrentStep(1)
          // Gọi action show error trên PageInfoStep trực tiếp
          setTimeout(() => {
            pageInfoStepRef.current?.showSlugError('Slug đã tồn tại, vui lòng chọn slug khác!')
          }, 150)
        } else {
          toast.error(response?.status?.message || 'Có lỗi xảy ra')
        }
      }
    } catch (err: any) {
      const errorMessage = isEditMode ? 'Có lỗi khi cập nhật trang đặt chỗ' : 'Có lỗi khi tạo trang đặt chỗ'
      toast.error(err?.message || errorMessage)
    }
  }

  const handleClose = () => {
    router.back()
  }

  const handleStepClick = (stepId: number) => {
    // Chỉ cho phép chuyển tới step đã hoàn thành hoặc step hiện tại
    if (stepId <= currentStep) {
      setCurrentStep(stepId)
    }
  }

  const renderBack = () => {
    return (
      <Button
        onClick={prevStep}
        disabled={currentStep === 1}
        variant="outline"
        size="lg"
        className="sm:w-auto border-orange-300 text-orange-600 hover:bg-orange-50 disabled:opacity-50 disabled:cursor-not-allowed h-12 px-6"
      >
        <ChevronLeft className="w-5 h-5 sm:mr-2" />
        <span className="hidden sm:block">Quay lại</span>
      </Button>
    )
  }

  const renderCloseButton = () => {
    if (isEditMode) {
      return null
    }

    return (
      <Button
        onClick={handleClose}
        variant="outline"
        size="lg"
        className="border-gray-300 text-gray-600 hover:bg-gray-50 h-12 px-4"
      >
        <X className="w-5 h-5 sm:mr-2" />
        <span className="hidden sm:block">Đóng</span>
      </Button>
    )
  }

  const renderActionButtons = () => {
    return (
      <div className="flex items-center gap-2">
        {/* Next/Finish Button */}
        {currentStep < TOTAL_STEPS
          ? (
              <Button
                onClick={handleNext}
                disabled={!canProceed()}
                size="lg"
                className="w-full sm:w-auto bg-orange-500 hover:bg-orange-600 text-white disabled:opacity-50 disabled:cursor-not-allowed h-12 px-6 shadow-lg"
              >
                <span className="hidden sm:block">Tiếp theo</span>
                <ChevronRight className="w-5 h-5 sm:ml-2" />
              </Button>
            )
          : (
              <Button
                onClick={handleFinish}
                disabled={!canProceed() || state.loading}
                size="lg"
                className="sm:w-auto bg-green-500 hover:bg-green-600 text-white disabled:opacity-50 disabled:cursor-not-allowed h-12 px-6 shadow-lg"
              >
                {state.loading
                  ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        <span className="hidden sm:block">Đang xử lý...</span>
                      </>
                    )
                  : (
                      <>
                        <Check className="w-5 h-5 sm:mr-2" />
                        <span className="hidden sm:block">
                          {isEditMode ? 'Cập nhật' : 'Hoàn thành'}
                        </span>
                      </>
                    )}
              </Button>
            )}
      </div>
    )
  }

  return (
    <div className="bg-white/90 backdrop-blur-md border-b border-orange-200/50 sticky top-0 z-50 shadow-sm">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-2 lg:py-2">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* Title Section with Close Button */}
          <div className="flex items-center justify-between lg:justify-start gap-4">
            {/* Close Button - Left side */}
            <div className="lg:hidden">
              {renderCloseButton()}
            </div>

            {/* Title */}
            <div className="text-center lg:text-left flex-1">
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">
                {isEditMode ? 'Chỉnh sửa trang đặt chỗ' : 'Tạo trang đặt chỗ'}
              </h1>
            </div>

            {/* Close Button - Desktop */}
            <div className="hidden lg:block">
              {renderCloseButton()}
            </div>
          </div>

          {/* Progress Steps - Desktop */}
          <div className="hidden lg:flex items-center space-x-4">
            {renderBack()}
            {STEPS.map((step, index) => {
              const Icon = step.icon
              const isActive = currentStep === step.id
              const isCompleted = currentStep > step.id
              return (
                <div key={step.id} className="flex items-center">
                  <button
                    type="button"
                    onClick={() => handleStepClick(step.id)}
                    disabled={step.id > currentStep}
                    className="focus:outline-none"
                  >
                    <div className={`
                      flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300
                      ${isActive
                  ? 'bg-orange-500 border-orange-500 text-white shadow-lg'
                  : isCompleted
                    ? 'bg-green-500 border-green-500 text-white'
                    : 'bg-white border-gray-300 text-gray-400'}
                      ${step.id <= currentStep ? 'cursor-pointer hover:scale-105' : 'cursor-not-allowed'}
                    `}
                    >
                      {isCompleted
                        ? (
                            <Check className="w-4 h-4" />
                          )
                        : (
                            <Icon className="w-4 h-4" />
                          )}
                    </div>
                  </button>
                  <div className="ml-3">
                    <div className={`text-sm font-medium ${
                      isActive ? 'text-orange-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                    }`}
                    >
                      Bước
                      {' '}
                      {step.id}
                    </div>
                    <div className={`text-xs ${
                      isActive ? 'text-orange-500' : isCompleted ? 'text-green-500' : 'text-gray-400'
                    }`}
                    >
                      {step.title}
                    </div>
                  </div>
                  {index < STEPS.length - 1 && (
                    <div className={`w-8 h-0.5 mx-4 ${isCompleted ? 'bg-green-300' : 'bg-gray-200'}`} />
                  )}
                </div>
              )
            })}
            {renderActionButtons()}
          </div>

          {/* Progress Steps - Mobile */}
          <div className="flex flex-col gap-3 lg:hidden">
            {/* Mobile Progress Steps */}
            <div className="flex items-center justify-center space-x-2">
              {renderBack()}
              {STEPS.map((step, index) => {
                const Icon = step.icon
                const isActive = currentStep === step.id
                const isCompleted = currentStep > step.id
                return (
                  <div key={step.id} className="flex items-center">
                    <button
                      type="button"
                      onClick={() => handleStepClick(step.id)}
                      disabled={step.id > currentStep}
                      className="focus:outline-none"
                    >
                      <div className={`
                        flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300
                        ${isActive
                    ? 'bg-orange-500 border-orange-500 text-white shadow-lg'
                    : isCompleted
                      ? 'bg-green-500 border-green-500 text-white'
                      : 'bg-white border-gray-300 text-gray-400'}
                        ${step.id <= currentStep ? 'cursor-pointer hover:scale-105' : 'cursor-not-allowed'}
                      `}
                      >
                        {isCompleted
                          ? (
                              <Check className="w-3 h-3" />
                            )
                          : (
                              <Icon className="w-3 h-3" />
                            )}
                      </div>
                    </button>
                    {index < STEPS.length - 1 && (
                      <div className={`w-4 h-0.5 mx-2 ${isCompleted ? 'bg-green-300' : 'bg-gray-200'}`} />
                    )}
                  </div>
                )
              })}
            </div>

            {/* Mobile Action Buttons */}
            <div className="flex items-center justify-center gap-2">
              {renderActionButtons()}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
