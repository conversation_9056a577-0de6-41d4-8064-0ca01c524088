import type { Metadata } from 'next'
import { GoogleAnalyticsProvider } from '@/components/analytics/GoogleAnalyticsProvider'
import { PostHogProvider } from '@/components/analytics/PostHogProvider'
import { ModalManager } from '@/components/modal'
import { Toaster } from '@/components/ui/sonner'
import { routing } from '@/libs/i18nNavigation'
import ProjectDashboardLayout from '@/modules/admin/layouts/ProjectDashboardLayout'
import { NextIntlClientProvider } from 'next-intl'
import { getMessages, setRequestLocale } from 'next-intl/server'
import { notFound } from 'next/navigation'
import '@/styles/global.css'

export const metadata: Metadata = {
  icons: [
    {
      rel: 'apple-touch-icon',
      url: '/apple-touch-icon.png',
    },
    {
      rel: 'icon',
      type: 'image/png',
      sizes: '32x32',
      url: '/favicon-32x32.png',
    },
    {
      rel: 'icon',
      type: 'image/png',
      sizes: '16x16',
      url: '/favicon-16x16.png',
    },
    {
      rel: 'icon',
      url: '/favicon.ico',
    },
  ],
}

export function generateStaticParams() {
  return routing.locales.map(locale => ({ locale }))
}

export default async function RootLayout(props: {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}) {
  const { locale } = await props.params

  if (!routing.locales.includes(locale)) {
    notFound()
  }

  setRequestLocale(locale)

  // Using internationalization in Client Components
  const messages = await getMessages()

  // The `suppressHydrationWarning` attribute in <body> is used to prevent hydration errors caused by Sentry Overlay,
  // which dynamically adds a `style` attribute to the body tag.

  return (
    <html lang={locale}>
      <body suppressHydrationWarning>
        <NextIntlClientProvider
          locale={locale}
          messages={messages}
        >
          <GoogleAnalyticsProvider>
            <PostHogProvider>
              <ProjectDashboardLayout>
                {props.children}
              </ProjectDashboardLayout>
            </PostHogProvider>
            <ModalManager />
          </GoogleAnalyticsProvider>
          <Toaster />
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
