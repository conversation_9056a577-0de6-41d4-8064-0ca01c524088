/* eslint-disable react-hooks-extra/no-direct-set-state-in-use-effect */
import { modalAPI } from '@/components/modal'
import { usePathname, useRouter } from '@/libs/i18nNavigation'
import { useCallback, useEffect, useState } from 'react'

export const useNavigation = () => {
  const router = useRouter()
  const pathname = usePathname()
  const [isNavigating, setIsNavigating] = useState(false)
  const [pendingPath, setPendingPath] = useState<string | null>(null)

  // Theo dõi thay đổi route để reset trạng thái navigating
  useEffect(() => {
    modalAPI.closeAllModals()

    if (pendingPath && pathname === pendingPath) {
      setIsNavigating(false)
      setPendingPath(null)
    }
  }, [pathname])

  const navigate = async (path: string) => {
    let loadingId = ''
    try {
      loadingId = modalAPI.showLoading()
      setIsNavigating(true)
      setPendingPath(path)

      // Prefetch tr<PERSON><PERSON><PERSON> khi navigate
      await router.prefetch(path)

      // Navigate đến trang mới
      await router.push(path)

      // Nếu navigate thành công ngay lập tức, reset trạng thái
      if (pathname === path) {
        setIsNavigating(false)
        setPendingPath(null)
        modalAPI.closeModal(loadingId)
      }
    } catch (error) {
      console.error('Navigation error:', error)
      setIsNavigating(false)
      setPendingPath(null)
      modalAPI.closeModal(loadingId)
    }
  }

  const navigateWithLoading = useCallback(async (path: string) => {
    if (isNavigating) {
      return
    } // Tránh navigate khi đang trong quá trình navigate

    try {
      setIsNavigating(true)
      setPendingPath(path)

      // Prefetch trước
      await router.prefetch(path)

      // Navigate
      await router.push(path)
    } catch (error) {
      console.error('Navigation error:', error)
      setIsNavigating(false)
      setPendingPath(null)
    }
  }, [router, isNavigating])

  return {
    navigate,
    navigateWithLoading,
    isNavigating,
    pendingPath,
  }
}
