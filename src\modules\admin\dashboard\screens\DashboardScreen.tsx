'use client'

import { But<PERSON> } from '@/components/ui/button'
import { useNavigation } from '@/hooks/useNavigation'
import { BookingPageGrid } from '@/modules/admin/dashboard/screens/components/BookingPageGrid'
import { appPaths } from '@/utils/app-routes'
import { Plus } from 'lucide-react'
import React from 'react'

export default function DashboardScreen() {
  const { navigate } = useNavigation()

  const handleCreateNew = () => {
    navigate(appPaths.admin.createBookingPage())
  }

  return (
    <div className="max-w-[68rem] mx-auto py-8 px-4">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div>
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <p className="text-gray-600 mt-1">Danh sách các Booking Page của bạn</p>
        </div>
        <Button onClick={handleCreateNew}>
          <Plus className="h-4 w-4 mr-2" />
          Tạo Booking Page
        </Button>
      </div>
      {/* Danh sách Booking Page */}
      <BookingPageGrid />
    </div>
  )
}
