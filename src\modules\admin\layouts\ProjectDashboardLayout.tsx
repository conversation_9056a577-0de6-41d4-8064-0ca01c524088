'use client'

import type { ReactNode } from 'react'

import { useSidebarState } from '@/hooks/use-sidebar-state'
import { useNavigation } from '@/hooks/useNavigation'
import { useFetchUserProfile, useUser, useUserLoading } from '@/modules/user/stores/user.store'
import { clearAuthTokens } from '@/services/auth'
import { appPaths } from '@/utils/app-routes'
import React, { memo } from 'react'
import AdminHeader from '../dashboard/screens/components/AdminHeader'

type ProjectDashboardLayoutProps = {
  children: ReactNode
}

const ProjectDashboardLayout = memo(({ children }: ProjectDashboardLayoutProps) => {
  // Đọc trạng thái sidebar từ cookie
  const [isOpen, setIsOpen] = useSidebarState()
  const { navigate } = useNavigation()
  const user = useUser()
  const fetchProfile = useFetchUserProfile()
  const isLoading = useUserLoading()

  React.useEffect(() => {
    if (!user) {
      fetchProfile()
    }
  }, [user, fetchProfile])

  const handleLogout = () => {
    clearAuthTokens()
    navigate(appPaths.auth.login())
  }

  return (
    <div className="min-h-screen flex flex-col">
      {/* Top navigation */}
      <AdminHeader />

      {/* Main content */}
      <main className="flex-1 bg-gray-50">
        {children}
      </main>
    </div>
  )
})

ProjectDashboardLayout.displayName = 'ProjectDashboardLayout'

export default ProjectDashboardLayout
