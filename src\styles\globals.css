/* Add these styles for the quick config panel */
.quick-config-overlay {
  backdrop-filter: blur(4px);
  background-color: rgba(0, 0, 0, 0.2);
}

/* Optional: Add smooth scrollbar for the quick config panel */
.quick-config-panel {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}

.quick-config-panel::-webkit-scrollbar {
  width: 6px;
}

.quick-config-panel::-webkit-scrollbar-track {
  background: transparent;
}

.quick-config-panel::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 3px;
}

/* MacBook Mock Styles */
.macbook-container {
  /* max-width: 1000px; */
  width: 100%;
  margin: 0 auto;
}

.macbook-screen {
  aspect-ratio: 16/10;
  position: relative;
}

.macbook-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.browser-content {
  flex: 1;
  overflow-y: auto;
  max-height: calc(100% - 30px);
}

/* Shake animation for input errors */
@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-2px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(2px);
  }
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}
