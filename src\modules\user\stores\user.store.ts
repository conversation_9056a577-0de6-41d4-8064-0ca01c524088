import type { UserProfile } from '../types/user.types'
import { userAPIs } from '@/apis/user.apis'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

interface UserState {
  user: UserProfile | null
  isLoading: boolean
  error: string | null
  fetchProfile: () => Promise<void>
  setUser: (_: UserProfile | null) => void
  reset: () => void
}

export const useUserStore = create<UserState>()(
  devtools(
    immer(set => ({
      user: null,
      isLoading: false,
      error: null,
      fetchProfile: async () => {
        set((state) => {
          state.isLoading = true
          state.error = null
        })
        try {
          const res = await userAPIs.getProfile()
          set((state) => {
            state.user = res.data ? res.data : null
            state.isLoading = false
          })
        } catch (err: any) {
          set((state) => {
            state.error = err?.message || 'Failed to fetch user'
            state.isLoading = false
          })
        }
      },
      setUser: user => set((state) => {
        state.user = user ?? null
      }),
      reset: () => set((state) => {
        state.user = null
        state.isLoading = false
        state.error = null
      }),
    })),
  ),
)

export const useUser = () => useUserStore(state => state.user)
export const useUserLoading = () => useUserStore(state => state.isLoading)
export const useUserError = () => useUserStore(state => state.error)
export const useFetchUserProfile = () => useUserStore(state => state.fetchProfile)
export const useSetUser = () => useUserStore(state => state.setUser)
export const useResetUser = () => useUserStore(state => state.reset)
