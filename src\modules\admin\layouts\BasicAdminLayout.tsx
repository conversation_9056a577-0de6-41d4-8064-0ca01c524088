'use client'

import React from 'react'
import AdminHeader from '../dashboard/screens/components/AdminHeader'

interface AdminLayoutProps {
  children: React.ReactNode
}

/**
 * Layout component for admin pages
 */
const BasicAdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Top navigation */}
      <AdminHeader />

      {/* Main content */}
      <main className="flex-1 bg-gray-50">
        {children}
      </main>
    </div>
  )
}

export default BasicAdminLayout
